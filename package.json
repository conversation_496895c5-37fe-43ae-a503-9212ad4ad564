{"name": "terminal-portfolio", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@react-three/drei": "^9.105.6", "@react-three/fiber": "^8.16.6", "@react-three/rapier": "^1.3.1", "@types/three": "^0.178.1", "framer-motion": "^10.18.0", "leva": "^0.9.35", "meshline": "^3.3.0", "react": "^18.3.1", "react-dom": "^18.3.1", "styled-components": "^6.1.19", "three": "^0.164.1"}, "devDependencies": {"@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/styled-components": "^5.1.34", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.6.0", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.8.3", "vite": "^5.4.19"}, "main": "index.js", "keywords": [], "author": "", "license": "ISC", "description": ""}